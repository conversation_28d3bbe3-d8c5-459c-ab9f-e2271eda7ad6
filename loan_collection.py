
# Copyright (c) 2023, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
import json
from typing import Dict, List, Any, Optional

# Constants
PAYMENT_MODES_NACH = ('E-NACH', 'Manual NACH')
PAYMENT_MODES_CHEQUE = ('Cheque', 'FPDC', 'NPDC')
DEFAULT_LIMIT = 500  # Increased for better performance with pagination
TOLERANCE_AMOUNT = 0.01

# Bank configurations
BANK_CONFIGS = {
    'AU Small Finance Bank': {
        'credit_account_no': '****************'
    },
    'HDFC': {
        'user_number': 'NACH00000000023302'
    }
}

# Performance optimization: Cache frequently accessed data
_NBFC_SETTINGS_CACHE = None
_CACHE_TIMESTAMP = None
CACHE_DURATION = 300  # 5 minutes

def execute(filters: Optional[Dict] = None) -> tuple:
    """Main execution function for the report with performance monitoring."""
    import time
    start_time = time.time()

    try:
        filters = filters or {}

        # Add default limit if not specified
        if 'limit' not in filters:
            filters['limit'] = DEFAULT_LIMIT

        # Debug: Log filters being used
        frappe.log_error(f"Loan collection filters: {filters}", "Debug Info")

        columns = get_columns()
        data = get_data(filters)

        # Log performance metrics
        execution_time = time.time() - start_time
        frappe.log_error(f"Loan collection execution time: {execution_time:.2f}s", "Performance Info")

        if execution_time > 5:  # Log slow queries
            frappe.log_error(
                f"Slow loan collection report: {execution_time:.2f}s",
                "Performance Warning"
            )

        return columns, data

    except Exception as e:
        # Log error with shorter message to avoid character limit
        error_msg = str(e)[:80] + "..." if len(str(e)) > 80 else str(e)
        frappe.log_error(f"Loan collection report error: {error_msg}")
        return get_columns(), []

def build_conditions(filters: Dict[str, Any]) -> tuple:
    """Build SQL WHERE conditions based on filters with SQL injection protection."""
    conditions = ""
    params = {}
    excluded_fields = {"from_date", "to_date", "status", "bucket", "repayment_mode", "due_by", "limit"}

    # Valid field names to prevent SQL injection through field names
    valid_fields = {
        "applicant_type", "company", "loan_type", "cost_center", "applicant",
        "loan_status", "state", "zone", "branch_code"
    }

    param_counter = 0

    for field, value in filters.items():
        if not value or field in excluded_fields:
            continue

        if field == "from_date":
            if filters.get("due_by") == "Due Today":
                conditions += " AND A.posting_date = CURDATE()"
            elif filters.get("to_date"):
                param_counter += 1
                from_param = f"from_date_{param_counter}"
                to_param = f"to_date_{param_counter}"
                conditions += f" AND A.posting_date BETWEEN %({from_param})s AND %({to_param})s"
                params[from_param] = filters['from_date']
                params[to_param] = filters['to_date']
        elif field == "repayment_mode":
            param_counter += 1
            param_name = f"repayment_mode_{param_counter}"
            conditions += f" AND LR.repayment_mode LIKE %({param_name})s"
            params[param_name] = f"%{value}%"
        elif field in valid_fields:
            param_counter += 1
            param_name = f"{field}_{param_counter}"
            conditions += f" AND A.{field} = %({param_name})s"
            params[param_name] = value

    # Debug: Log the generated conditions and params
    frappe.log_error(f"Generated conditions: {conditions}", "Debug SQL")
    frappe.log_error(f"Generated params: {params}", "Debug SQL")

    return conditions, params

def get_monthly_date_conditions(due_by: str) -> str:
    """Get date conditions for monthly filters."""
    if due_by == "Due This Month":
        return " AND RS.payment_date BETWEEN DATE_ADD(CURDATE(), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(CURDATE())"
    elif due_by == "Due Next Month":
        return " AND RS.payment_date BETWEEN DATE_ADD(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(DATE_ADD(CURDATE(), INTERVAL 1 MONTH))"
    return ""

def get_address_concat() -> str:
    """Get standardized address concatenation SQL."""
    return """CONCAT(
        COALESCE(addr.address_line1, ''),
        CASE WHEN addr.area IS NOT NULL THEN CONCAT(', ', addr.area) ELSE '' END,
        CASE WHEN addr.city IS NOT NULL THEN CONCAT(', ', addr.city) ELSE '' END,
        CASE WHEN addr.district IS NOT NULL THEN CONCAT(', ', addr.district) ELSE '' END,
        CASE WHEN addr.state IS NOT NULL THEN CONCAT(', ', addr.state) ELSE '' END
    )"""

def get_monthly_data(filters: Dict[str, Any]) -> List[Dict]:
    """Get data for monthly due filters with performance optimization."""
    conditions = get_monthly_date_conditions(filters.get("due_by", ""))

    # Optimized query with essential fields only
    query = f"""
        SELECT STRAIGHT_JOIN
            LN.name AS name,
            LN.name AS loan,
            LN.loan_application,
            LN.bucket,
            LN.applicant_type,
            LN.applicant,
            LN.posting_date,
            LN.loan_amount,
            LN.disbursement_date,
            LN.disbursed_amount,
            LN.email_id,
            LN.loan_status,
            LN.state,
            LN.due_date AS actual_due_date,
            LN.mode_of_payment AS repayment_mode,
            LN.umrn_,
            RS.payment_date AS due_date,
            RS.total_payment AS emi
        FROM `tabRepayment Schedule` RS
        INNER JOIN `tabLoan` LN ON LN.name = RS.parent
        WHERE RS.docstatus = 1 {conditions}
        ORDER BY RS.payment_date ASC
        LIMIT {DEFAULT_LIMIT}
    """

    base_data = frappe.db.sql(query, as_dict=True)

    if not base_data:
        return []

    # Get customer data in batch
    customer_data = get_customer_data_batch(base_data)

    # Process results
    result = []
    for row in base_data:
        # Determine status
        current_date = frappe.utils.nowdate()
        payment_date = str(row['due_date'])

        if current_date == payment_date:
            status = 'Payment Due Today'
        elif current_date < payment_date:
            status = 'Future Payment Date'
        else:
            status = 'Payment Date Passed'

        # Apply status filter
        if filters.get('status') and status not in filters['status']:
            continue

        # Get customer info
        customer_info = customer_data.get(row['applicant'], {})

        # Get cheque number
        cheque_no = 'NA'
        if row['repayment_mode'] in PAYMENT_MODES_NACH:
            cheque_no = row.get('umrn_', 'NA') or 'NA'

        result_row = {
            'name': row['name'],
            'loan': row['loan'],
            'loan_application': row['loan_application'],
            'bucket': row['bucket'],
            'applicant_type': row['applicant_type'],
            'applicant': row['applicant'],
            'applicant_name': customer_info.get('customer_name', ''),
            'applicant_mobile': customer_info.get('mobile_no', ''),
            'applicant_address': customer_info.get('address', ''),
            'posting_date': row['posting_date'],
            'loan_amount': row['loan_amount'],
            'disbursement_date': row['disbursement_date'],
            'disbursed_amount': row['disbursed_amount'],
            'email_id': row['email_id'],
            'loan_status': row['loan_status'],
            'state': row['state'],
            'due_date': row['due_date'],
            'actual_due_date': row['actual_due_date'],
            'emi': row['emi'],
            'status': status,
            'cheque_no': cheque_no,
            'repayment_mode': row['repayment_mode'],
            'collection_agent_name': ''
        }
        result.append(result_row)

    return result

def get_data(filters: Dict[str, Any]) -> List[Dict]:
    """Main data retrieval function."""
    try:
        # Handle monthly filters
        if filters.get("due_by") in ["Due This Month", "Due Next Month"]:
            data = get_monthly_data(filters)
        else:
            data = get_standard_data(filters)

        # Apply bucket filtering if specified
        if filters.get("bucket"):
            data = [record for record in data if record.get("bucket") == filters["bucket"]]

        return data
    except Exception as e:
        # Log error with shorter message to avoid character limit
        error_msg = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)
        frappe.log_error(f"Loan collection data error: {error_msg}")
        return []

def get_standard_data(filters: Dict[str, Any]) -> List[Dict]:
    """Get standard loan collection data with simplified approach for debugging."""
    conditions, params = build_conditions(filters)

    # Simplified query with basic fields to test first
    query = f"""
        SELECT
            A.name AS name,
            A.loan AS loan,
            A.applicant_type,
            A.applicant,
            A.posting_date AS due_date,
            A.payable_principal_amount,
            A.interest_amount,
            A.paid_principal_amount,
            A.paid_interest_amount,
            A.penalty_amount,
            RS.payment_date,
            loan.cost_center,
            loan.bucket,
            loan.due_date AS actual_due_date,
            loan.mode_of_payment AS repayment_mode,
            COALESCE(loan.umrn_, 'NA') as cheque_no,
            cust.customer_name as applicant_name,
            cust.mobile_no as applicant_mobile,
            CONCAT(
                COALESCE(addr.address_line1, ''),
                CASE WHEN addr.area IS NOT NULL THEN CONCAT(', ', addr.area) ELSE '' END,
                CASE WHEN addr.city IS NOT NULL THEN CONCAT(', ', addr.city) ELSE '' END,
                CASE WHEN addr.district IS NOT NULL THEN CONCAT(', ', addr.district) ELSE '' END,
                CASE WHEN addr.state IS NOT NULL THEN CONCAT(', ', addr.state) ELSE '' END
            ) AS applicant_address,
            cc.cost_center_number AS branch_code,
            cc.state AS branch_state,
            cc.zone
        FROM `tabLoan Interest Accrual` A
        INNER JOIN `tabLoan` loan ON A.loan = loan.name
        INNER JOIN `tabRepayment Schedule` RS ON RS.name = A.repayment_schedule_name
            AND RS.docstatus = 1
        LEFT JOIN `tabCustomer` cust ON A.applicant = cust.name
        LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
        LEFT JOIN `tabCost Center` cc ON cc.name = loan.cost_center
        WHERE A.docstatus = 1 {conditions}
        ORDER BY RS.payment_date ASC
        LIMIT {DEFAULT_LIMIT}
    """

    # Get data with parameters
    data = frappe.db.sql(query, params, as_dict=True)

    # Process results to add calculated fields
    result = []
    for row in data:
        # Calculate EMI and amounts
        emi = row['payable_principal_amount'] + row['interest_amount']

        outstanding = (row['payable_principal_amount'] + row['interest_amount'] -
                      row['paid_principal_amount'] - row['paid_interest_amount'])

        # Determine status
        if outstanding <= TOLERANCE_AMOUNT:
            status = 'Paid'
        elif outstanding > TOLERANCE_AMOUNT and (row['paid_principal_amount'] + row['paid_interest_amount']) > 0:
            status = 'Partially Paid'
        elif frappe.utils.nowdate() == str(row['payment_date']):
            status = 'Payment Today'
        elif frappe.utils.nowdate() < str(row['payment_date']):
            status = 'On Time'
        else:
            status = 'Delayed'

        # Apply status filter
        if filters.get('status') and status not in filters['status']:
            continue

        # Build result row
        result_row = {
            'name': row['name'],
            'loan': row['loan'],
            'applicant_type': row['applicant_type'],
            'applicant': row['applicant'],
            'applicant_name': row.get('applicant_name', ''),
            'applicant_mobile': row.get('applicant_mobile', ''),
            'applicant_address': row.get('applicant_address', ''),
            'due_date': row['due_date'],
            'actual_due_date': row['actual_due_date'],
            'emi': emi,
            'penalty': row['penalty_amount'],
            'vas': 0,  # Simplified for now
            'shortfall_amount': outstanding,
            'total_amount': (row['paid_principal_amount'] + row['paid_interest_amount'] +
                           row['penalty_amount']),
            'status': status,
            'bucket': row['bucket'],
            'cost_center': row['cost_center'],
            'branch_code': row.get('branch_code', ''),
            'branch_state': row.get('branch_state', ''),
            'zone': row.get('zone', ''),
            'repayment_mode': row['repayment_mode'],
            'collection_agent_name': '',  # Simplified for now
            'cheque_no': row.get('cheque_no', 'NA')
        }
        result.append(result_row)

    return result

# Performance optimization helper functions
def get_customer_data_batch(base_data: List[Dict]) -> Dict[str, Dict]:
    """Get customer and address data in batch to avoid N+1 queries."""
    applicants = list(set(row['applicant'] for row in base_data if row['applicant']))
    if not applicants:
        return {}

    query = """
        SELECT
            cust.name,
            cust.customer_name,
            cust.mobile_no,
            CONCAT(
                COALESCE(addr.address_line1, ''),
                CASE WHEN addr.area IS NOT NULL THEN CONCAT(', ', addr.area) ELSE '' END,
                CASE WHEN addr.city IS NOT NULL THEN CONCAT(', ', addr.city) ELSE '' END,
                CASE WHEN addr.district IS NOT NULL THEN CONCAT(', ', addr.district) ELSE '' END,
                CASE WHEN addr.state IS NOT NULL THEN CONCAT(', ', addr.state) ELSE '' END
            ) AS address
        FROM `tabCustomer` cust
        LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
        WHERE cust.name IN %(applicants)s
    """

    result = frappe.db.sql(query, {"applicants": applicants}, as_dict=True)
    return {row['name']: row for row in result}

def get_cost_center_data_batch(base_data: List[Dict]) -> Dict[str, Dict]:
    """Get cost center data in batch."""
    cost_centers = list(set(row['cost_center'] for row in base_data if row['cost_center']))
    if not cost_centers:
        return {}

    query = """
        SELECT name, cost_center_number, state, zone
        FROM `tabCost Center`
        WHERE name IN %(cost_centers)s
    """

    result = frappe.db.sql(query, {"cost_centers": cost_centers}, as_dict=True)
    return {row['name']: row for row in result}

def get_sales_order_data_batch(base_data: List[Dict]) -> Dict[str, float]:
    """Get sales order amounts in batch."""
    accrual_names = [row['name'] for row in base_data]
    if not accrual_names:
        return {}

    query = """
        SELECT
            RS.name as repayment_schedule_name,
            SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total ELSE SO.rounded_total END) as amount
        FROM `tabRepayment Schedule` RS
        INNER JOIN `tabSales Order` SO ON SO.repayment_schedule = RS.name AND SO.docstatus = 1
        INNER JOIN `tabLoan Interest Accrual` A ON A.repayment_schedule_name = RS.name
        WHERE A.name IN %(accrual_names)s
        GROUP BY RS.name
    """

    result = frappe.db.sql(query, {"accrual_names": accrual_names}, as_dict=True)
    return {row['repayment_schedule_name']: row['amount'] or 0 for row in result}

def get_collection_agent_data_batch(base_data: List[Dict]) -> Dict[str, str]:
    """Get collection agent data in batch."""
    accrual_names = [row['name'] for row in base_data]
    if not accrual_names:
        return {}

    query = """
        SELECT
            LRD.loan_interest_accrual,
            GROUP_CONCAT(LR.collection_agent_name) as collection_agent_name
        FROM `tabLoan Repayment Detail` LRD
        INNER JOIN `tabLoan Repayment` LR ON LR.name = LRD.parent AND LR.docstatus = 1
        WHERE LRD.loan_interest_accrual IN %(accrual_names)s AND LRD.docstatus = 1
        GROUP BY LRD.loan_interest_accrual
    """

    result = frappe.db.sql(query, {"accrual_names": accrual_names}, as_dict=True)
    return {row['loan_interest_accrual']: row['collection_agent_name'] or '' for row in result}

def get_cheque_data_batch(base_data: List[Dict]) -> Dict[str, Dict]:
    """Get cheque data in batch."""
    loan_names = list(set(row['loan'] for row in base_data))
    if not loan_names:
        return {}

    # Get PDC data
    pdc_query = """
        SELECT parent as loan, cheque_no, idx
        FROM `tabPDC Cheque Representation Table`
        WHERE parent IN %(loan_names)s
        AND presented_on IS NULL
        AND status IS NULL
        ORDER BY parent, idx
    """

    # Get SPDC data
    spdc_query = """
        SELECT parent as loan, cheque_no, idx
        FROM `tabSPDC Cheque Representation Table`
        WHERE parent IN %(loan_names)s
        AND presented_on IS NULL
        AND status IS NULL
        ORDER BY parent, idx
    """

    pdc_result = frappe.db.sql(pdc_query, {"loan_names": loan_names}, as_dict=True)
    spdc_result = frappe.db.sql(spdc_query, {"loan_names": loan_names}, as_dict=True)

    # Organize by loan
    cheque_data = {}
    for row in pdc_result:
        if row['loan'] not in cheque_data:
            cheque_data[row['loan']] = {'pdc': [], 'spdc': []}
        cheque_data[row['loan']]['pdc'].append(row)

    for row in spdc_result:
        if row['loan'] not in cheque_data:
            cheque_data[row['loan']] = {'pdc': [], 'spdc': []}
        cheque_data[row['loan']]['spdc'].append(row)

    return cheque_data

def get_cheque_number(row: Dict, cheque_data: Dict) -> str:
    """Get cheque number for a specific row."""
    if row['repayment_mode'] in PAYMENT_MODES_NACH:
        return row.get('umrn_', 'NA') or 'NA'
    elif row['repayment_mode'] in PAYMENT_MODES_CHEQUE:
        loan_cheques = cheque_data.get(row['loan'], {'pdc': [], 'spdc': []})

        # Find matching PDC cheque by idx
        for pdc in loan_cheques['pdc']:
            if pdc['idx'] == row.get('rs_idx'):
                return pdc['cheque_no']

        # Fallback to first available SPDC
        if loan_cheques['spdc']:
            return loan_cheques['spdc'][0]['cheque_no']

        return 'NA'
    else:
        return 'NA'

def get_cheque_no_case() -> str:
    """Get the CASE statement for cheque_no field."""
    return f"""
        CASE
            WHEN loan.mode_of_payment IN {PAYMENT_MODES_CHEQUE} THEN COALESCE((
                SELECT pdc.cheque_no FROM `tabPDC Cheque Representation Table` pdc
                WHERE A.loan = pdc.parent AND pdc.presented_on IS NULL
                      AND pdc.status IS NULL AND RS.idx = pdc.idx
                ORDER BY idx LIMIT 1
            ), (
                SELECT spdc.cheque_no FROM `tabSPDC Cheque Representation Table` spdc
                WHERE A.loan = spdc.parent AND spdc.presented_on IS NULL
                      AND spdc.status IS NULL
                ORDER BY idx LIMIT 1
            ), 'NA')
            WHEN loan.mode_of_payment IN {PAYMENT_MODES_NACH} THEN COALESCE(loan.umrn_, 'NA')
            ELSE 'NA'
        END
    """


def get_columns() -> List[Dict[str, str]]:
    """Get column definitions for the report."""
    return [
        {"label": "Source Application Number", "fieldtype": "Data", "fieldname": "loan_application"},
        {"label": "Loan", "fieldtype": "Link", "fieldname": "loan", "options": "Loan"},
        {"label": "Customer Relationship Id", "fieldtype": "Data", "fieldname": "customer_id"},
        {"label": "Applicant", "fieldtype": "Data", "fieldname": "applicant"},
        {"label": "Applicant Name", "fieldtype": "Data", "fieldname": "applicant_name"},
        {"label": "Applicant Type", "fieldtype": "Data", "fieldname": "applicant_type"},
        {"label": "Applicant Mobile", "fieldtype": "Data", "fieldname": "applicant_mobile"},
        {"label": "Sanction Date", "fieldtype": "Date", "fieldname": "posting_date"},
        {"label": "Sanction Amount", "fieldtype": "Currency", "fieldname": "loan_amount"},
        {"label": "Disbursement Date", "fieldtype": "Date", "fieldname": "disbursement_date"},
        {"label": "Disbursed Amount", "fieldtype": "Currency", "fieldname": "disbursed_amount"},
        {"label": "Email Id", "fieldtype": "Data", "fieldname": "email_id"},
        {"label": "Branch Code", "fieldtype": "Data", "fieldname": "branch_code"},
        {"label": "Branch Name", "fieldtype": "Data", "fieldname": "cost_center"},
        {"label": "Branch State", "fieldtype": "Data", "fieldname": "branch_state"},
        {"label": "Branch Zone", "fieldtype": "Data", "fieldname": "zone"},
        {"label": "Loan Status", "fieldtype": "Data", "fieldname": "loan_status"},
        {"label": "Applicant Address", "fieldtype": "Data", "fieldname": "applicant_address"},
        {"label": "EMI Date", "fieldtype": "Date", "fieldname": "due_date"},
        {"label": "Due Date", "fieldtype": "Date", "fieldname": "actual_due_date"},
        {"label": "EMI", "fieldtype": "Currency", "fieldname": "emi"},
        {"label": "Total Amount (Paid)", "fieldtype": "Currency", "fieldname": "total_amount"},
        {"label": "Bucket", "fieldtype": "Data", "fieldname": "bucket"},
        {"label": "Status", "fieldtype": "Data", "fieldname": "status"},
        {"label": "Action", "fieldtype": "Button", "fieldname": "action", "options": "dropdown", "width": "200px"}
    ]

# Bank-specific data processing functions
@frappe.whitelist()
def get_filtered_data(data_array: str, nach_type: str) -> None:
    """
    Process loan data for specific bank types (AU Small Finance Bank, HDFC, FPDC).

    Args:
        data_array: JSON string containing loan data
        nach_type: Type of bank processing ('AU Small Finance Bank', 'HDFC', 'FPDC')
    """
    try:
        final_obj_list = []
        nach_array = json.loads(data_array)

        for item in nach_array:
            loan_no = item.get("loan")
            if not loan_no:
                continue

            try:
                loan_doc = frappe.get_doc("Loan", loan_no)
                loan_app_doc = frappe.get_doc("Loan Application", loan_doc.loan_application)
                sponsor_bank = loan_app_doc.select_sponsor_bank

                processed_item = process_bank_specific_data(item, loan_doc, sponsor_bank, nach_type)
                if processed_item:
                    final_obj_list.append(processed_item)

            except frappe.DoesNotExistError:
                frappe.log_error(f"Loan or Loan Application not found for loan: {loan_no}")
                continue

        frappe.response.message = {'finalObjList': final_obj_list}

    except (json.JSONDecodeError, Exception) as e:
        frappe.log_error(f"Error in get_filtered_data: {str(e)}")
        frappe.response.message = {'error': 'Failed to process data'}

def process_bank_specific_data(item: Dict, loan_doc: Any, sponsor_bank: str, nach_type: str) -> Optional[Dict]:
    """Process data based on bank type."""
    if nach_type == 'AU Small Finance Bank' and sponsor_bank == 'AU Small Finance Bank':
        return {
            'UMRN': getattr(loan_doc, 'umrn', ''),
            'ConsumerReferenceNo': item["loan"],
            'Amount': item.get('emi', 0),
            'SettlementDate': item.get("due_date", ''),
            'CreditAccountNo': BANK_CONFIGS['AU Small Finance Bank']['credit_account_no']
        }

    elif nach_type == 'HDFC' and sponsor_bank == 'HDFC':
        return {
            'UserNumber': BANK_CONFIGS['HDFC']['user_number'],
            'SettlementDate': item.get("due_date", ''),
            'UMRN': getattr(loan_doc, 'umrn', ''),
            'Amount': item.get('emi', 0),
            'ConsumerReferenceNo': item["loan"]
        }

    elif nach_type == 'FPDC':
        return {
            'LoanNo': item["loan"],
            'ChequeNo': item.get('cheque_no', ''),
            'EMIamount': item.get('emi', 0),
            'BankName': getattr(loan_doc, 'bank_name_', ''),
        }

    return None


@frappe.whitelist()
def get_hdfc_filtered_data(data_array: str) -> None:
    """
    Process loan data specifically for HDFC bank.

    Args:
        data_array: JSON string containing loan data
    """
    try:
        final_obj_list = []
        enach_array = json.loads(data_array)

        for item in enach_array:
            loan_no = item.get("loan")
            if not loan_no:
                continue

            try:
                loan_doc = frappe.get_doc("Loan", loan_no)
                loan_app_doc = frappe.get_doc("Loan Application", loan_doc.loan_application)
                sponsor_bank = loan_app_doc.select_sponsor_bank

                if sponsor_bank == "HDFC":
                    obj_to_export = {
                        'UserNumber': BANK_CONFIGS['HDFC']['user_number'],
                        'SettlementDate': item.get("due_date", ''),
                        'UMRN': getattr(loan_doc, 'umrn', ''),
                        'Amount': getattr(loan_app_doc, 'repayment_amount', 0),
                        'ReferenceNo': ''
                    }
                    final_obj_list.append(obj_to_export)

            except frappe.DoesNotExistError:
                frappe.log_error(f"Loan or Loan Application not found for loan: {loan_no}")
                continue

        frappe.response.message = final_obj_list

    except (json.JSONDecodeError, Exception) as e:
        frappe.log_error(f"Error in get_hdfc_filtered_data: {str(e)}")
        frappe.response.message = {'error': 'Failed to process HDFC data'}

# Legacy function names for backward compatibility
@frappe.whitelist()
def GetFilteredData(DataArray, nach_type):
    """Legacy wrapper for get_filtered_data."""
    return get_filtered_data(DataArray, nach_type)

@frappe.whitelist()
def GetHDFCFilteredData(DataArray):
    """Legacy wrapper for get_hdfc_filtered_data."""
    return get_hdfc_filtered_data(DataArray)
