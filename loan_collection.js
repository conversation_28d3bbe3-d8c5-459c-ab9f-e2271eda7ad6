// Copyright (c) 2023, <PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt
/* eslint-disable */


const status_list = {
  "Payment Today": {
    value: "Payment Today",
    get_label: function (d) {
      return this.value;
    },
    description: "To be collected today",
    txt_cls: "text-success",
    actions: ["_loan_receive", "_loan_skip", "_loan_send_reminder"],
  },
  "Partially Paid": {
    value: "Partially Paid",
    get_label: function (d) {
      return this.value;
    },
    description: "Full amount not repaid",
    txt_cls: "text-warning",
    actions: ["_loan_receive", "_loan_skip", "_loan_send_reminder"],
  },
  Delayed: {
    value: "Delayed",
    get_label: function (d) {
      return `Delayed by ${moment
        .duration(moment().diff(d.due_date))
        .humanize()}`;
    },
    description: "Fully unpaid after due date",
    txt_cls: "text-danger",
    actions: [
      "_loan_receive",
      "_loan_skip",
      "_loan_send_reminder",
      "_loan_add_vas",
    ],
  },
  "On Time": {
    value: "On Time",
    get_label: function (d) {
      return this.value;
    },
    description: "Upcoming payments",
    txt_cls: "text-info",
  },
  Paid: {
    value: "Paid",
    get_label: function (d) {
      return this.value;
    },
    description: "Fully paid EMIs",
    txt_cls: "",
    actions: ["_loan_view_repayments", "_loan_add_vas"],
  },
  "Payment Due Today": {
    value: "Payment Due Today",
    get_label: function (d) {
      return this.value;
    },
    description: "Due Today",
    txt_cls: "text-info",
  },
  "Future Payment Date": {
    value: "Future Payment Date",
    get_label: function (d) {
      return this.value;
    },
    description: "Upcoming payments",
    txt_cls: "",
  },
  "Payment Date Passed": {
    value: "Payment Date Passed",
    get_label: function (d) {
      return this.value;
    },
    description: "Past payments",
    txt_cls: "",
  },
  "": {
    value: "",
    get_label: function (d) {
      return this.value;
    },
    description: "Past payments",
    txt_cls: "",
  },
};

const emi_actions = {
  _loan_receive: function (e) {
    var data = $(e.currentTarget).data();

    frappe.call({
      method: "nbfc.nbfc.custom_scripts.loan.loan.make_repayment_entry",
      freeze: true,
      args: {
        loan: data.loan,
      },
      callback: function (r) {
        if (r.message) {
          var doc = frappe.model.sync(r.message)[0];
          frappe.set_route("Form", doc.doctype, doc.name);
        } else {
          frappe.throw("Unable to process repayment");
        }
      },
    });
  },
  _loan_skip: function (e) {
    var data = $(e.currentTarget).data();

    frappe.msgprint("feature pending...");
    return;
    frappe.call({
      method: "nbfc.nbfc.custom_scripts.loan.loan.make_loan_write_off",
      freeze: true,
      args: {
        loan: data.loan,
      },
      callback: function (r) {
        if (r.message) {
          var doc = frappe.model.sync(r.message)[0];
          frappe.set_route("Form", doc.doctype, doc.name);
        }
      },
    });
  },
  _loan_send_reminder: function (e) {
    var data = $(e.currentTarget).data();

    frappe.call({
      method: "nbfc.nbfc.custom_scripts.loan.loan.make_loan_reminder",
      freeze: true,
      args: {
        data: data,
      },
      callback: function (r) {
        var email = new frappe.views.CommunicationComposer(r.message);
        email.dialog.set_value("content", r.message.message);
        email.dialog.set_secondary_action(function () {
          frappe.call({
            method: "nbfc.nbfc.doctype.loan_reminder.loan_reminder._trash",
            args: { name: r.message._reminder },
          });
          email.dialog.hide();
          email.clear_cache();
        });
      },
    });
  },
  _loan_view_repayments: function (e) {
    var data = $(e.currentTarget).data();
    frappe.route_options = { loan_interest_accrual: data.accrual };
    frappe.set_route("List", "Loan Repayment");
  },
  _loan_add_vas: function (e) {
    var data = $(e.currentTarget).data();

    frappe.call({
      method: "nbfc.nbfc.custom_scripts.loan.loan.make_vas",
      freeze: true,
      args: {
        loan_name: data.loan,
        accrual: data.accrual,
      },
      callback: function (r) {
        if (r.message) {
          var doc = frappe.model.sync(r.message)[0];
          frappe.set_route("Form", doc.doctype, doc.name);
        }
      },
    });
  },
};

for (let k in emi_actions) {
  frappe.query_report[k] = emi_actions[k];
}

frappe.query_reports["Loan Collection"] = {
	
    onload: function (report) {
      report.page.add_inner_button(
        "AU Nach Settlement Format",
        function () {
          var filteredData = frappe.query_report.data;
          console.log(filteredData);
          var ENachArray = [];
          filteredData.forEach(function (item) {
            if (item.repayment_mode == "E-NACH") {
              ENachArray.push(item);
            }
          });
		   let AUExcelHeader=["UMRN", "Consumer Refrence No", "Amount", "Settlement Date", "Credit Account No"]
		  exportToExcel(ENachArray,AUExcelHeader,"AU Small Finance Bank");
        },
        "Download Settlement Format"
      );

	report.page.add_inner_button(
        "HDFC Nach Settlement Format",
		function () {
      var filteredData = frappe.query_report.data;
      console.log(filteredData);
      var ENachArray = [];
      filteredData.forEach(function (item) {
        if (item.repayment_mode == "E-NACH") {
          ENachArray.push(item);
        }
      });
			let HDFCExcelheader=["UserNumber", "SettlementDate", "UMRN", "Amount","Reference No"]
			exportToExcel(ENachArray,HDFCExcelheader,"HDFC");
			},
			  "Download Settlement Format"
	);

  report.page.add_inner_button(
    "Cheque Settlement Format",
    function(){
      var filteredData = frappe.query_report.data;
      console.log(filteredData);
      var fpdcArray = [];
      filteredData.forEach(function (item) {
        if (item.repayment_mode == "FPDC") {
          fpdcArray.push(item);
        }
      });
      // console.log(fpdcArray)
      let chequeSetlmntExcelHeader=["Loan No"," Cheque No","EMI amount","Bank Name"]
      exportToExcel(fpdcArray,chequeSetlmntExcelHeader,"FPDC");
    },
    'Download Settlement Format'

  );
		 
  
  
		
  
      // var testButton = `<div class="dropdown">
      //     <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
      //         Download Settlement Format
      //     </button>
      //     <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
      //         <a class="dropdown-item" href="#" onclick="GetDataAU()">NACH Settlement Format AU</a>
      //         <a class="dropdown-item" href="#" onclick="GetDataHDFC()">NACH Settlement Format HDFC</a>
      //     </div>
      // </div>`;
      // $('.custom-actions.hidden-xs.hidden-md').append(testButton);
  
      //})
      
        
    },
    formatter: function (value, row, column, data, default_formatter) {
      value = default_formatter(value, row, column, data);
  
      if (column.fieldname == "applicant" && data) {
        let doctype = data.applicant_type
          ? data.applicant_type.toLowerCase()
          : "";
        value = `<a href="/app/${doctype}/${value}" data-doctype="${doctype}" data-name="${value}" data-value="${value}">
                      ${value}</a>`;
      } else if (column.fieldname == "status" && data && value != "??") {
        // console.log(value)
        value = `<span class="${status_list[value].txt_cls}">${status_list[
          value
        ].get_label(data)}</span>`;
      } else if (column.fieldname == "action" && data) {
        value = get_actions_html(data);
      }
      return value;
    },
    filters: [
      {
        fieldname: "company",
        label: __("Company"),
        fieldtype: "Link",
        options: "Company",
        default: frappe.defaults.get_user_default("Company"),
        reqd: 1,
      },
      {
        fieldname: "applicant_type",
        label: __("Applicant Type"),
        fieldtype: "Select",
        options: ["", "Customer", "Employee"],
        default : "Customer",
        on_change: function () {
          frappe.query_report.set_filter_value("applicant", "");
          frappe.query_report.refresh();
        },
      },
      {
        fieldname: "applicant",
        label: __("Applicant"),
        fieldtype: "Dynamic Link",
        get_options: function () {
          var applicant_type =
            frappe.query_report.get_filter_value("applicant_type");
          var applicant = frappe.query_report.get_filter_value("applicant");
          if (applicant && !applicant_type) {
            frappe.throw(__("Please select Applicant Type first"));
          }
          return applicant_type;
        },
      },
      {
        fieldname: "from_date",
        label: __("From Date"),
        fieldtype: "Date",
        default: frappe.defaults.get_user_default("year_start_date"),
        reqd: 1,
      },
      {
        fieldname: "to_date",
        label: __("To Date"),
        fieldtype: "Date",
        default: frappe.datetime.get_today(),
        reqd: 1,
      },
      {
        fieldname: "status",
        label: __("Status"),
        fieldtype: "MultiSelectList",
        get_data: function () {
          return Object.values(status_list);
        },
      },
      {
        fieldname: "bucket",
        label: __("Bucket"),
        fieldtype: "Data",
      },
      {
        fieldname: "repayment_mode",
        label: __("Mode of Payment"),
        fieldtype: "Link",
        //options: "\nECS\nCheque\nCash\nPayment Gateway\nBank Transfer",
        options:"Mode of Payment"
      },
      {
        fieldname: "due_by",
        label: __("Due By"),
        fieldtype: "Select",
        options: "\nDue Today\nDue This Month\nDue Next Month",
      },
  
      // {
      // 	fieldname: "collection_agent_name",
      // 	label: __("Collection Agent"),
      // 	fieldtype: "Link",
      // 	options: "Employee"
      // }
    ],
  };

function focus_row(_t) {
  if (_t) {
    event.target.closest(".dt-row").classList.add("dt-row--highlight");
  } else {
    event.target.closest(".dt-row").classList.remove("dt-row--highlight");
  }
}

function get_actions_html(data) {
  let actions = status_list[data.status]
    ? status_list[data.status].actions || []
    : [];
  let html = `<div onmouseover="focus_row(1)" onmouseout="focus_row(0)" class="dropdown text-capitalize position-absolute w-100 h-100" style="top:0; left:0;">`;

  if (actions.length > 1) {
    html += `<button class="dropdown-toggle rounded-0 btn btn-sm btn-sp-effect h-100 w-100" data-toggle="dropdown">Actions</button>
				<ul class="dropdown-menu">
					${actions
            .map(
              (a) =>
                `<li><a class="grey-link dropdown-item" onclick="return false;" data-action="${a}" data-loan="${
                  data.loan
                }" data-accrual="${data.name}" href="#">${a
                  .replace("_loan_", "")
                  .replaceAll("_", " ")}</a></li>`
            )
            .join("")}
				</ul>`;
  } else if (actions.length == 1) {
    html += `<button class="text-capitalize rounded-0 btn btn-sm btn-sp-effect h-100 w-100" data-action="${
      actions[0]
    }" data-loan="${data.loan}" data-accrual="${data.name}">${actions[0]
      .replace("_loan_", "")
      .replaceAll("_", " ")}</button>`;
  }

  html += "</div>";

  return html;
}



function exportToExcel(NachArray,header,nach_type) {
  console.log(NachArray)
	frappe.call({
		method: "nbfc.nbfc.report.loan_collection.loan_collection.GetFilteredData",
		freeze:1,
		args: {
			DataArray:JSON.stringify(NachArray),
			nach_type: nach_type
		},
		callback: (r) => {
			let finalObjList=r.message.finalObjList
			const xlsxCDN = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.4/xlsx.full.min.js'; 
			const script = document.createElement('script');
			script.src = xlsxCDN;
			script.onload = function () {
			//exportToExcel(finalObjList,header);
			const worksheet = XLSX.utils.aoa_to_sheet([
				header,
				...finalObjList.map(obj => Object.values(obj))
			]);
			const workbook = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
			const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
			const data = new Blob([excelBuffer], { type: 'application/octet-stream' });
			const url = URL.createObjectURL(data);
			const link = document.createElement('a');
			link.href = url;
			link.setAttribute('download', 'NachSettlementFormat.xlsx');
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			};
			document.head.appendChild(script);	
		}
	})
}













