
# Copyright (c) 2023, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
import json
from typing import Dict, List, Any, Optional

# Constants
PAYMENT_MODES_NACH = ('E-NACH', 'Manual NACH')
PAYMENT_MODES_CHEQUE = ('Cheque', 'FPDC', 'NPDC')
DEFAULT_LIMIT = 100
TOLERANCE_AMOUNT = 0.01

# Bank configurations
BANK_CONFIGS = {
    'AU Small Finance Bank': {
        'credit_account_no': '****************'
    },
    'HDFC': {
        'user_number': 'NACH00000000023302'
    }
}

def execute(filters: Optional[Dict] = None) -> tuple:
    """Main execution function for the report."""
    try:
        return get_columns(), get_data(filters or {})
    except Exception as e:
        frappe.log_error(f"Error in loan collection report: {str(e)}")
        return get_columns(), []

def build_conditions(filters: Dict[str, Any]) -> str:
    """Build SQL WHERE conditions based on filters."""
    conditions = ""
    excluded_fields = {"from_date", "to_date", "status", "bucket", "repayment_mode", "due_by"}

    for field, value in filters.items():
        if not value:
            continue

        if field == "from_date":
            if filters.get("due_by") == "Due Today":
                conditions += " AND A.posting_date = CURDATE()"
            elif filters.get("to_date"):
                conditions += f" AND A.posting_date BETWEEN '{filters['from_date']}' AND '{filters['to_date']}'"
        elif field == "repayment_mode":
            conditions += f" AND LR.repayment_mode LIKE '%{value}%'"
        elif field not in excluded_fields:
            conditions += f" AND A.{field} = '{value}'"

    return conditions

def get_monthly_date_conditions(due_by: str) -> str:
    """Get date conditions for monthly filters."""
    if due_by == "Due This Month":
        return " AND RS.payment_date BETWEEN DATE_ADD(CURDATE(), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(CURDATE())"
    elif due_by == "Due Next Month":
        return " AND RS.payment_date BETWEEN DATE_ADD(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(DATE_ADD(CURDATE(), INTERVAL 1 MONTH))"
    return ""

def get_address_concat() -> str:
    """Get standardized address concatenation SQL."""
    return """CONCAT(
        COALESCE(addr.address_line1, ''),
        CASE WHEN addr.area IS NOT NULL THEN CONCAT(', ', addr.area) ELSE '' END,
        CASE WHEN addr.city IS NOT NULL THEN CONCAT(', ', addr.city) ELSE '' END,
        CASE WHEN addr.district IS NOT NULL THEN CONCAT(', ', addr.district) ELSE '' END,
        CASE WHEN addr.state IS NOT NULL THEN CONCAT(', ', addr.state) ELSE '' END
    )"""

def get_monthly_data(filters: Dict[str, Any]) -> List[Dict]:
    """Get data for monthly due filters."""
    conditions = get_monthly_date_conditions(filters.get("due_by", ""))
    address_concat = get_address_concat()

    query = f"""
        SELECT
            LN.name AS name, LN.name AS loan, LN.loan_application,
            LN.bucket, LN.applicant_type, LN.applicant,
            LN.posting_date, LN.loan_amount, LN.disbursement_date,
            LN.disbursed_amount, LN.email_id, LN.loan_status, LN.state,
            cust.customer_name as applicant_name,
            cust.mobile_no as applicant_mobile,
            {address_concat} AS applicant_address,
            '' AS bucket,
            RS.payment_date AS due_date,
            LN.due_date AS actual_due_date,
            RS.total_payment AS emi,
            CASE
                WHEN CURDATE() = RS.payment_date THEN 'Payment Due Today'
                WHEN CURDATE() < RS.payment_date THEN 'Future Payment Date'
                WHEN CURDATE() > RS.payment_date THEN 'Payment Date Passed'
                ELSE '??'
            END AS status,
            CASE
                WHEN LN.mode_of_payment IN {PAYMENT_MODES_NACH} THEN COALESCE(LN.umrn_, 'NA')
                ELSE 'NA'
            END AS cheque_no,
            LN.mode_of_payment AS repayment_mode,
            '' AS collection_agent_name
        FROM `tabRepayment Schedule` RS
        RIGHT JOIN `tabLoan` LN ON LN.name = RS.parent
        LEFT JOIN `tabCustomer` cust ON LN.applicant = cust.name
        LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
        LEFT JOIN `tabSales Order` SO ON SO.repayment_schedule = RS.name AND SO.docstatus = 1
        WHERE RS.docstatus = 1 {conditions}
        GROUP BY RS.name
        HAVING status {'IN' if filters.get('status') else 'NOT IN'} %(status_list)s
        ORDER BY RS.payment_date ASC
        LIMIT {DEFAULT_LIMIT}
    """

    return frappe.db.sql(query, {
        "status_list": tuple(filters.get("status") or ["*"])
    }, as_dict=True)

def get_data(filters: Dict[str, Any]) -> List[Dict]:
    """Main data retrieval function."""
    try:
        # Handle monthly filters
        if filters.get("due_by") in ["Due This Month", "Due Next Month"]:
            data = get_monthly_data(filters)
        else:
            data = get_standard_data(filters)

        # Apply bucket filtering if specified
        if filters.get("bucket"):
            data = [record for record in data if record.get("bucket") == filters["bucket"]]

        return data
    except Exception as e:
        frappe.log_error(f"Error retrieving loan collection data: {str(e)}")
        return []

def get_standard_data(filters: Dict[str, Any]) -> List[Dict]:
    """Get standard loan collection data."""
    conditions = build_conditions(filters)
    address_concat = get_address_concat()

    query = f"""
        SELECT
            A.name AS name, A.loan AS loan,
            loan.cost_center, cc.cost_center_number AS branch_code,
            cc.state AS branch_state, cc.zone,
            loan.bucket, A.applicant_type, A.applicant,
            cust.customer_name AS applicant_name,
            cust.mobile_no AS applicant_mobile,
            {address_concat} AS applicant_address,
            A.posting_date AS due_date,
            loan.due_date AS actual_due_date,
            (A.payable_principal_amount + A.interest_amount +
             COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total
                              ELSE SO.rounded_total END), 0)) AS emi,
            A.penalty_amount AS penalty,
            COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total
                             ELSE SO.rounded_total END), 0) AS vas,
            (A.payable_principal_amount + A.interest_amount +
             COALESCE(SUM(CASE WHEN SO.disable_rounded_total
                              THEN SO.grand_total - SO.grand_total * SO.per_billed / 100
                              ELSE SO.rounded_total - SO.rounded_total * SO.per_billed / 100 END), 0) -
             A.paid_principal_amount - A.paid_interest_amount) AS shortfall_amount,
            (A.paid_principal_amount + A.paid_interest_amount + A.penalty_amount +
             COALESCE(SUM(CASE WHEN SO.disable_rounded_total
                              THEN SO.grand_total * SO.per_billed / 100
                              ELSE SO.rounded_total * SO.per_billed / 100 END), 0)) AS total_amount,
            CASE
                WHEN (A.payable_principal_amount + A.interest_amount -
                      A.paid_principal_amount - A.paid_interest_amount) <= {TOLERANCE_AMOUNT} THEN 'Paid'
                WHEN CURDATE() = RS.payment_date THEN 'Payment Today'
                WHEN (A.payable_principal_amount + A.interest_amount -
                      A.paid_principal_amount - A.paid_interest_amount) > {TOLERANCE_AMOUNT}
                     AND (A.paid_principal_amount + A.paid_interest_amount) > 0 THEN 'Partially Paid'
                WHEN CURDATE() < RS.payment_date THEN 'On Time'
                WHEN CURDATE() > RS.payment_date THEN 'Delayed'
                ELSE '??'
            END AS status,
            loan.mode_of_payment AS repayment_mode,
            GROUP_CONCAT(LR.collection_agent_name) AS collection_agent_name,
            {get_cheque_no_case()} AS cheque_no
        FROM `tabLoan Interest Accrual` A
        LEFT JOIN `tabCustomer` cust ON A.applicant = cust.name
        LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
        LEFT JOIN `tabRepayment Schedule` RS ON RS.name = A.repayment_schedule_name AND RS.docstatus = 1
        LEFT JOIN `tabSales Order` SO ON SO.repayment_schedule = RS.name AND SO.docstatus = 1
        LEFT JOIN `tabLoan Repayment Detail` LRD ON LRD.loan_interest_accrual = A.name AND LRD.docstatus = 1
        LEFT JOIN `tabLoan Repayment` LR ON LR.name = LRD.parent AND LR.docstatus = 1
        LEFT JOIN `tabLoan` loan ON A.loan = loan.name
        LEFT JOIN `tabCost Center` cc ON cc.name = loan.cost_center
        WHERE A.docstatus = 1 {conditions}
        GROUP BY A.name
        HAVING status {'IN' if filters.get('status') else 'NOT IN'} %(status_list)s
        ORDER BY RS.payment_date ASC
        LIMIT {DEFAULT_LIMIT}
    """

    return frappe.db.sql(query, {
        "status_list": tuple(filters.get("status") or ["*"])
    }, as_dict=True)

def get_cheque_no_case() -> str:
    """Get the CASE statement for cheque_no field."""
    return f"""
        CASE
            WHEN loan.mode_of_payment IN {PAYMENT_MODES_CHEQUE} THEN COALESCE((
                SELECT pdc.cheque_no FROM `tabPDC Cheque Representation Table` pdc
                WHERE A.loan = pdc.parent AND pdc.presented_on IS NULL
                      AND pdc.status IS NULL AND RS.idx = pdc.idx
                ORDER BY idx LIMIT 1
            ), (
                SELECT spdc.cheque_no FROM `tabSPDC Cheque Representation Table` spdc
                WHERE A.loan = spdc.parent AND spdc.presented_on IS NULL
                      AND spdc.status IS NULL
                ORDER BY idx LIMIT 1
            ), 'NA')
            WHEN loan.mode_of_payment IN {PAYMENT_MODES_NACH} THEN COALESCE(loan.umrn_, 'NA')
            ELSE 'NA'
        END
    """


def get_columns() -> List[Dict[str, str]]:
    """Get column definitions for the report."""
    return [
        {"label": "Source Application Number", "fieldtype": "Data", "fieldname": "loan_application"},
        {"label": "Loan", "fieldtype": "Link", "fieldname": "loan", "options": "Loan"},
        {"label": "Customer Relationship Id", "fieldtype": "Data", "fieldname": "customer_id"},
        {"label": "Applicant", "fieldtype": "Data", "fieldname": "applicant"},
        {"label": "Applicant Name", "fieldtype": "Data", "fieldname": "applicant_name"},
        {"label": "Applicant Type", "fieldtype": "Data", "fieldname": "applicant_type"},
        {"label": "Applicant Mobile", "fieldtype": "Data", "fieldname": "applicant_mobile"},
        {"label": "Sanction Date", "fieldtype": "Date", "fieldname": "posting_date"},
        {"label": "Sanction Amount", "fieldtype": "Currency", "fieldname": "loan_amount"},
        {"label": "Disbursement Date", "fieldtype": "Date", "fieldname": "disbursement_date"},
        {"label": "Disbursed Amount", "fieldtype": "Currency", "fieldname": "disbursed_amount"},
        {"label": "Email Id", "fieldtype": "Data", "fieldname": "email_id"},
        {"label": "Branch Code", "fieldtype": "Data", "fieldname": "branch_code"},
        {"label": "Branch Name", "fieldtype": "Data", "fieldname": "cost_center"},
        {"label": "Branch State", "fieldtype": "Data", "fieldname": "branch_state"},
        {"label": "Branch Zone", "fieldtype": "Data", "fieldname": "zone"},
        {"label": "Loan Status", "fieldtype": "Data", "fieldname": "loan_status"},
        {"label": "Applicant Address", "fieldtype": "Data", "fieldname": "applicant_address"},
        {"label": "EMI Date", "fieldtype": "Date", "fieldname": "due_date"},
        {"label": "Due Date", "fieldtype": "Date", "fieldname": "actual_due_date"},
        {"label": "EMI", "fieldtype": "Currency", "fieldname": "emi"},
        {"label": "Total Amount (Paid)", "fieldtype": "Currency", "fieldname": "total_amount"},
        {"label": "Bucket", "fieldtype": "Data", "fieldname": "bucket"},
        {"label": "Status", "fieldtype": "Data", "fieldname": "status"},
        {"label": "Action", "fieldtype": "Button", "fieldname": "action", "options": "dropdown", "width": "200px"}
    ]

# Bank-specific data processing functions
@frappe.whitelist()
def get_filtered_data(data_array: str, nach_type: str) -> None:
    """
    Process loan data for specific bank types (AU Small Finance Bank, HDFC, FPDC).

    Args:
        data_array: JSON string containing loan data
        nach_type: Type of bank processing ('AU Small Finance Bank', 'HDFC', 'FPDC')
    """
    try:
        final_obj_list = []
        nach_array = json.loads(data_array)

        for item in nach_array:
            loan_no = item.get("loan")
            if not loan_no:
                continue

            try:
                loan_doc = frappe.get_doc("Loan", loan_no)
                loan_app_doc = frappe.get_doc("Loan Application", loan_doc.loan_application)
                sponsor_bank = loan_app_doc.select_sponsor_bank

                processed_item = process_bank_specific_data(item, loan_doc, sponsor_bank, nach_type)
                if processed_item:
                    final_obj_list.append(processed_item)

            except frappe.DoesNotExistError:
                frappe.log_error(f"Loan or Loan Application not found for loan: {loan_no}")
                continue

        frappe.response.message = {'finalObjList': final_obj_list}

    except (json.JSONDecodeError, Exception) as e:
        frappe.log_error(f"Error in get_filtered_data: {str(e)}")
        frappe.response.message = {'error': 'Failed to process data'}

def process_bank_specific_data(item: Dict, loan_doc: Any, sponsor_bank: str, nach_type: str) -> Optional[Dict]:
    """Process data based on bank type."""
    if nach_type == 'AU Small Finance Bank' and sponsor_bank == 'AU Small Finance Bank':
        return {
            'UMRN': getattr(loan_doc, 'umrn', ''),
            'ConsumerReferenceNo': item["loan"],
            'Amount': item.get('emi', 0),
            'SettlementDate': item.get("due_date", ''),
            'CreditAccountNo': BANK_CONFIGS['AU Small Finance Bank']['credit_account_no']
        }

    elif nach_type == 'HDFC' and sponsor_bank == 'HDFC':
        return {
            'UserNumber': BANK_CONFIGS['HDFC']['user_number'],
            'SettlementDate': item.get("due_date", ''),
            'UMRN': getattr(loan_doc, 'umrn', ''),
            'Amount': item.get('emi', 0),
            'ConsumerReferenceNo': item["loan"]
        }

    elif nach_type == 'FPDC':
        return {
            'LoanNo': item["loan"],
            'ChequeNo': item.get('cheque_no', ''),
            'EMIamount': item.get('emi', 0),
            'BankName': getattr(loan_doc, 'bank_name_', ''),
        }

    return None


@frappe.whitelist()
def get_hdfc_filtered_data(data_array: str) -> None:
    """
    Process loan data specifically for HDFC bank.

    Args:
        data_array: JSON string containing loan data
    """
    try:
        final_obj_list = []
        enach_array = json.loads(data_array)

        for item in enach_array:
            loan_no = item.get("loan")
            if not loan_no:
                continue

            try:
                loan_doc = frappe.get_doc("Loan", loan_no)
                loan_app_doc = frappe.get_doc("Loan Application", loan_doc.loan_application)
                sponsor_bank = loan_app_doc.select_sponsor_bank

                if sponsor_bank == "HDFC":
                    obj_to_export = {
                        'UserNumber': BANK_CONFIGS['HDFC']['user_number'],
                        'SettlementDate': item.get("due_date", ''),
                        'UMRN': getattr(loan_doc, 'umrn', ''),
                        'Amount': getattr(loan_app_doc, 'repayment_amount', 0),
                        'ReferenceNo': ''
                    }
                    final_obj_list.append(obj_to_export)

            except frappe.DoesNotExistError:
                frappe.log_error(f"Loan or Loan Application not found for loan: {loan_no}")
                continue

        frappe.response.message = final_obj_list

    except (json.JSONDecodeError, Exception) as e:
        frappe.log_error(f"Error in get_hdfc_filtered_data: {str(e)}")
        frappe.response.message = {'error': 'Failed to process HDFC data'}

# Legacy function names for backward compatibility
@frappe.whitelist()
def GetFilteredData(DataArray, nach_type):
    """Legacy wrapper for get_filtered_data."""
    return get_filtered_data(DataArray, nach_type)

@frappe.whitelist()
def GetHDFCFilteredData(DataArray):
    """Legacy wrapper for get_hdfc_filtered_data."""
    return get_hdfc_filtered_data(DataArray)
