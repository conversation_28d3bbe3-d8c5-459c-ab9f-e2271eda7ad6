# # Copyright (c) 2023, <PERSON><PERSON><PERSON> and contributors
# # For license information, please see license.txt

# import frappe


# def execute(filters=None):
# 	return get_columns(), get_data(filters)

# def get_data(filters=None):
# 	conditions = ""
# 	for f in filters:
# 		if f == "from_date":
# 			if filters.get("due_by") == "Due Today":
# 				conditions += f""" AND A.posting_date BETWEEN CURDATE() AND CURDATE() """
# 			else:
# 				conditions += f""" AND A.posting_date BETWEEN "{filters.get("from_date")}" AND "{filters.get("to_date")}" """
# 		elif filters.get(f) and f not in ["from_date", "to_date", "status","bucket","repayment_mode","due_by"]:
# 			conditions += f""" AND A.{f} = "{filters.get(f)}" """
# 		elif f == "repayment_mode":

# 			conditions += f""" AND LR.{f} LIKE '%%{filters.get(f)}%%' """
# 	nbfc_setting = frappe.get_doc("NBFC Setting")
# 	bucket = [{"bucket":a.bucket,"min_delay":a.min_delay,"max_delay":a.max_delay} for a in nbfc_setting.bucket]
# 	if filters.get("due_by") in  ["Due This Month","Due Next Month"]:
# 		if filters.get("due_by") == "Due This Month":
# 			conditions = f""" AND RS.payment_date BETWEEN date_add(CURDATE(),interval -DAY(CURDATE())+1 DAY) AND LAST_DAY(CURDATE()) """
# 		else:
# 			conditions = f""" AND RS.payment_date BETWEEN date_add(DATE_ADD(CURDATE(), INTERVAL 1 MONTH),interval -DAY(CURDATE())+1 DAY) AND LAST_DAY(DATE_ADD(CURDATE(), INTERVAL 1 MONTH)) """	
# 		data = frappe.db.sql(f"""
# 			SELECT  
# 					LN.name AS name,
# 					LN.name AS loan,
# 					LN.loan_application,
# 					LN.bucket AS bucket,
# 					LN.applicant_type AS applicant_type,
# 					LN.applicant AS applicant,
# 					LN.posting_date,
# 					LN.loan_amount,
# 					LN.disbursement_date,
# 					LN.disbursed_amount,
# 					LN.email_id,
# 					LN.loan_status,
# 					LN.state,
# 					cust.customer_name as applicant_name,
# 					cust.mobile_no as applicant_mobile,
# 					CONCAT(addr.address_line1, ", ",coalesce(CONCAT(addr.area,", ")," "),coalesce(CONCAT(addr.city,", "), " "), coalesce(CONCAT(addr.district,", "), " ") ,coalesce(addr.state, " ") ) as applicant_address,
# 					" " AS bucket,
# 					RS.payment_date AS due_date,
# 					LN.due_date AS actual_due_date,
# 					RS.total_payment AS emi,
# 					(CASE
# 						WHEN CURDATE() = RS.payment_date THEN "Payment Due Today"
# 						WHEN CURDATE() < RS.payment_date THEN "Future Payment Date"
# 						WHEN CURDATE() > RS.payment_date THEN "Payment Date Passed"
# 						ELSE "??"
# 					END ) AS status,
# 					(CASE
# 						WHEN(
# 							LN.mode_of_payment in ('E-NACH','Manual NACH')) THEN 
# 							LN.umrn_
# 						ELSE 'NA'
# 					END) AS cheque_no,
# 					LN.mode_of_payment AS repayment_mode,
# 					" " AS collection_agent_name
# 			FROM `tabRepayment Schedule` AS RS 
# 			Right JOIN `tabLoan` AS LN
# 				ON LN.name = RS.parent
# 			LEFT JOIN `tabCustomer` as cust on LN.applicant = cust.name
# 			LEFT JOIN `tabAddress` as addr on cust.customer_primary_address = addr.name
# 			LEFT JOIN `tabSales Order` AS SO
# 				ON SO.repayment_schedule = RS.name
# 				AND SO.docstatus = 1
# 			WHERE RS.docstatus = 1
# 			{conditions}
# 			GROUP BY RS.name
# 			HAVING status {"" if filters.get("status") else "not"} in %(status_list)s
# 			ORDER BY RS.payment_date asc
# 			LIMIT 100
# 		""", {
# 			"status_list": tuple(filters.get("status") or ["*"])
# 		}, as_dict=1)
# 	else:
# 		data = frappe.db.sql(f"""
# 			SELECT  A.name AS name,
# 					A.loan AS loan,
# 					loan.cost_center,
# 					cc.cost_center_number as branch_code,
# 					cc.state as branch_state,
# 					cc.zone,
# 					loan.bucket AS bucket,
# 					A.applicant_type AS applicant_type,
# 					A.applicant AS applicant,
# 					cust.customer_name as applicant_name,
# 					cust.mobile_no as applicant_mobile,
# 					CONCAT(addr.address_line1, ", ",coalesce(addr.area," "), ", ",coalesce(addr.city, " "),", ", coalesce(addr.district, " ") ,", ",coalesce(addr.state, " ") ) as applicant_address,
# 					A.posting_date AS due_date,
# 					loan.due_date as actual_due_date,
# 					(
# 						A.payable_principal_amount
# 						+ A.interest_amount
# 						+ CASE
# 							WHEN SO.disable_rounded_total THEN COALESCE(SUM(SO.grand_total), 0)
# 							ELSE COALESCE(SUM(SO.rounded_total), 0)
# 						END
# 					) AS emi,
# 					A.penalty_amount AS penalty,
# 					CASE
# 						WHEN SO.disable_rounded_total THEN COALESCE(SUM(SO.grand_total), 0)
# 						ELSE COALESCE(SUM(SO.rounded_total), 0)
# 					END AS vas,
# 					(
# 						A.payable_principal_amount
# 						+ A.interest_amount
# 						+ (CASE
# 							WHEN SO.disable_rounded_total THEN COALESCE(SUM(SO.grand_total - SO.grand_total * SO.per_billed / 100), 0)
# 							ELSE COALESCE(SUM(SO.rounded_total - SO.rounded_total * SO.per_billed / 100), 0)
# 						END)
# 						- A.paid_principal_amount
# 						- A.paid_interest_amount
# 					) AS shortfall_amount,
# 					(
# 						A.paid_principal_amount
# 						+ A.paid_interest_amount
# 						+ A.penalty_amount
# 						+ (CASE
# 							WHEN SO.disable_rounded_total THEN COALESCE(SUM(SO.grand_total * SO.per_billed / 100), 0)
# 							ELSE COALESCE(SUM(SO.rounded_total * SO.per_billed / 100), 0)
# 						END)
# 					) AS total_amount,
# 					(CASE
# 						WHEN (A.payable_principal_amount
# 							+ A.interest_amount
# 							- A.paid_principal_amount
# 							- A.paid_interest_amount) <= 0.01 THEN "Paid"
# 						WHEN CURDATE() = RS.payment_date THEN "Payment Today"
# 						WHEN (A.payable_principal_amount
# 							+ A.interest_amount
# 							- A.paid_principal_amount
# 							- A.paid_interest_amount) > 0.01 
# 							AND (A.paid_principal_amount
# 							+ A.paid_interest_amount) > 0  THEN "Partially Paid"
# 						WHEN CURDATE() < RS.payment_date THEN "On Time"
# 						WHEN CURDATE() > RS.payment_date THEN "Delayed"
# 						ELSE "??"
# 					END ) AS status,
# 					loan.mode_of_payment AS repayment_mode,
# 					GROUP_CONCAT(LR.collection_agent_name) AS collection_agent_name,
					
# 					(CASE

# 						WHEN(loan.mode_of_payment in ('Cheque','FPDC','NPDC')) THEN COALESCE(
# 						(
# 							SELECT pdc.cheque_no
# 							FROM `tabPDC Cheque Representation Table` AS pdc 
# 							WHERE A.loan = pdc.parent
# 								AND pdc.presented_on IS NULL 
# 								AND pdc.status IS NULL
# 								AND RS.idx = pdc.idx
# 							ORDER BY idx
# 							LIMIT 1
# 						),
# 						(
# 							SELECT spdc.cheque_no
# 							FROM `tabSPDC Cheque Representation Table` AS spdc 
# 							WHERE A.loan = spdc.parent
# 								AND spdc.presented_on IS NULL 
# 								AND spdc.status IS NULL
# 							ORDER BY idx
# 							LIMIT 1
# 						), 'NA')
# 						WHEN(
# 							loan.mode_of_payment in ('E-NACH','Manual NACH')) THEN 
# 							loan.umrn_
# 						ELSE 'NA'
# 					END) AS cheque_no

# 			FROM `tabLoan Interest Accrual` AS A
# 			LEFT JOIN `tabCustomer` as cust on A.applicant = cust.name
			
# 			LEFT JOIN `tabAddress` as addr on cust.customer_primary_address = addr.name
# 			LEFT JOIN `tabRepayment Schedule` AS RS 
# 				ON RS.name = A.repayment_schedule_name 
# 				AND RS.docstatus = 1
# 			LEFT JOIN `tabSales Order` AS SO
# 				ON SO.repayment_schedule = RS.name
# 				AND SO.docstatus = 1
# 			LEFT JOIN `tabLoan Repayment Detail` AS LRD
# 				ON LRD.loan_interest_accrual = A.name
# 				AND LRD.docstatus=1
# 			LEFT JOIN `tabLoan Repayment` AS LR
# 				ON LR.name = LRD.parent
# 				AND LR.docstatus = 1
# 			LEFT JOIN `tabLoan` as loan on A.loan = loan.name
# 			LEFT JOIN `tabCost Center` cc ON cc.name = loan.cost_center

# 			WHERE A.docstatus = 1
# 			{conditions}
# 			GROUP BY A.name
# 			HAVING status {"" if filters.get("status") else "not"} in %(status_list)s
# 			ORDER BY RS.payment_date asc
# 			LIMIT 100
# 		""", {
# 			"status_list": tuple(filters.get("status") or ["*"])
# 		}, as_dict=1)

# 		if filters.get("bucket") and filters.get("bucket") != "":
# 			filtered = []

# 			for dat in data:
# 				if dat.bucket == filters.get("bucket"):
# 					filtered.append(dat)
# 		else:
# 			filtered = data
# 	# frappe.msgprint(repr(filtered))
# 	return filtered
# Copyright (c) 2023, Guitaa and contributors
# For license information, please see license.txt

import frappe
import json

def execute(filters=None):
	return get_columns(), get_data(filters or {})

def build_conditions(filters):
	conditions = ""
	for f in filters:
		if f == "from_date":
			if filters.get("due_by") == "Due Today":
				conditions += " AND A.posting_date = CURDATE()"
			else:
				conditions += f""" AND A.posting_date BETWEEN "{filters.get("from_date")}" AND "{filters.get("to_date")}" """
		elif f == "repayment_mode":
			conditions += f""" AND LR.repayment_mode LIKE '%%{filters.get(f)}%%' """
		elif filters.get(f) and f not in ["from_date", "to_date", "status", "bucket", "repayment_mode", "due_by"]:
			conditions += f""" AND A.{f} = "{filters.get(f)}" """
	return conditions

def get_data(filters):
	conditions = build_conditions(filters)
	nbfc_setting = frappe.get_doc("NBFC Setting")
	bucket = [{"bucket": a.bucket, "min_delay": a.min_delay, "max_delay": a.max_delay} for a in nbfc_setting.bucket]

	# If due_by is 'Due This Month' or 'Due Next Month'
	if filters.get("due_by") in ["Due This Month", "Due Next Month"]:
		if filters["due_by"] == "Due This Month":
			conditions = " AND RS.payment_date BETWEEN DATE_ADD(CURDATE(), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(CURDATE()) "
		else:
			conditions = " AND RS.payment_date BETWEEN DATE_ADD(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), INTERVAL -DAY(CURDATE())+1 DAY) AND LAST_DAY(DATE_ADD(CURDATE(), INTERVAL 1 MONTH)) "
		
		data = frappe.db.sql(f"""
			SELECT
				LN.name AS name, LN.name AS loan, LN.loan_application,
				LN.bucket, LN.applicant_type, LN.applicant,
				LN.posting_date, LN.loan_amount, LN.disbursement_date,
				LN.disbursed_amount, LN.email_id, LN.loan_status, LN.state,
				cust.customer_name as applicant_name, cust.mobile_no as applicant_mobile,
				CONCAT(addr.address_line1, ", ", COALESCE(addr.area, ""), ", ",
					   COALESCE(addr.city, ""), ", ", COALESCE(addr.district, ""), ", ",
					   COALESCE(addr.state, "")) AS applicant_address,
				"" AS bucket,
				RS.payment_date AS due_date,
				LN.due_date AS actual_due_date,
				RS.total_payment AS emi,
				CASE
					WHEN CURDATE() = RS.payment_date THEN "Payment Due Today"
					WHEN CURDATE() < RS.payment_date THEN "Future Payment Date"
					WHEN CURDATE() > RS.payment_date THEN "Payment Date Passed"
					ELSE "??"
				END AS status,
				CASE
					WHEN LN.mode_of_payment IN ('E-NACH','Manual NACH') THEN LN.umrn_
					ELSE 'NA'
				END AS cheque_no,
				LN.mode_of_payment AS repayment_mode,
				"" AS collection_agent_name
			FROM `tabRepayment Schedule` RS
			RIGHT JOIN `tabLoan` LN ON LN.name = RS.parent
			LEFT JOIN `tabCustomer` cust ON LN.applicant = cust.name
			LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
			LEFT JOIN `tabSales Order` SO ON SO.repayment_schedule = RS.name AND SO.docstatus = 1
			WHERE RS.docstatus = 1 {conditions}
			GROUP BY RS.name
			HAVING status {"" if filters.get("status") else "not"} IN %(status_list)s
			ORDER BY RS.payment_date ASC
			LIMIT 100
		""", {"status_list": tuple(filters.get("status") or ["*"])}, as_dict=1)
	else:
		data = frappe.db.sql(f"""
			SELECT
				A.name AS name, A.loan AS loan,
				loan.cost_center, cc.cost_center_number AS branch_code,
				cc.state AS branch_state, cc.zone,
				loan.bucket, A.applicant_type, A.applicant,
				cust.customer_name AS applicant_name, cust.mobile_no AS applicant_mobile,
				CONCAT(addr.address_line1, ", ", COALESCE(addr.area, ""), ", ",
					   COALESCE(addr.city, ""), ", ", COALESCE(addr.district, ""), ", ",
					   COALESCE(addr.state, "")) AS applicant_address,
				A.posting_date AS due_date, loan.due_date AS actual_due_date,
				(A.payable_principal_amount + A.interest_amount
				 + COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total ELSE SO.rounded_total END), 0)) AS emi,
				A.penalty_amount AS penalty,
				COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total ELSE SO.rounded_total END), 0) AS vas,
				(A.payable_principal_amount + A.interest_amount
				 + COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total - SO.grand_total * SO.per_billed / 100 ELSE SO.rounded_total - SO.rounded_total * SO.per_billed / 100 END), 0)
				 - A.paid_principal_amount - A.paid_interest_amount) AS shortfall_amount,
				(A.paid_principal_amount + A.paid_interest_amount + A.penalty_amount
				 + COALESCE(SUM(CASE WHEN SO.disable_rounded_total THEN SO.grand_total * SO.per_billed / 100 ELSE SO.rounded_total * SO.per_billed / 100 END), 0)) AS total_amount,
				CASE
					WHEN (A.payable_principal_amount + A.interest_amount - A.paid_principal_amount - A.paid_interest_amount) <= 0.01 THEN "Paid"
					WHEN CURDATE() = RS.payment_date THEN "Payment Today"
					WHEN (A.payable_principal_amount + A.interest_amount - A.paid_principal_amount - A.paid_interest_amount) > 0.01
						 AND (A.paid_principal_amount + A.paid_interest_amount) > 0 THEN "Partially Paid"
					WHEN CURDATE() < RS.payment_date THEN "On Time"
					WHEN CURDATE() > RS.payment_date THEN "Delayed"
					ELSE "??"
				END AS status,
				loan.mode_of_payment AS repayment_mode,
				GROUP_CONCAT(LR.collection_agent_name) AS collection_agent_name,
				CASE
					WHEN loan.mode_of_payment IN ('Cheque','FPDC','NPDC') THEN COALESCE((
						SELECT pdc.cheque_no FROM `tabPDC Cheque Representation Table` pdc
						WHERE A.loan = pdc.parent AND pdc.presented_on IS NULL AND pdc.status IS NULL AND RS.idx = pdc.idx
						ORDER BY idx LIMIT 1
					), (
						SELECT spdc.cheque_no FROM `tabSPDC Cheque Representation Table` spdc
						WHERE A.loan = spdc.parent AND spdc.presented_on IS NULL AND spdc.status IS NULL
						ORDER BY idx LIMIT 1
					), 'NA')
					WHEN loan.mode_of_payment IN ('E-NACH','Manual NACH') THEN loan.umrn_
					ELSE 'NA'
				END AS cheque_no
			FROM `tabLoan Interest Accrual` A
			LEFT JOIN `tabCustomer` cust ON A.applicant = cust.name
			LEFT JOIN `tabAddress` addr ON cust.customer_primary_address = addr.name
			LEFT JOIN `tabRepayment Schedule` RS ON RS.name = A.repayment_schedule_name AND RS.docstatus = 1
			LEFT JOIN `tabSales Order` SO ON SO.repayment_schedule = RS.name AND SO.docstatus = 1
			LEFT JOIN `tabLoan Repayment Detail` LRD ON LRD.loan_interest_accrual = A.name AND LRD.docstatus = 1
			LEFT JOIN `tabLoan Repayment` LR ON LR.name = LRD.parent AND LR.docstatus = 1
			LEFT JOIN `tabLoan` loan ON A.loan = loan.name
			LEFT JOIN `tabCost Center` cc ON cc.name = loan.cost_center
			WHERE A.docstatus = 1 {conditions}
			GROUP BY A.name
			HAVING status {"" if filters.get("status") else "not"} IN %(status_list)s
			ORDER BY RS.payment_date ASC
			LIMIT 100
		""", {"status_list": tuple(filters.get("status") or ["*"])}, as_dict=1)

		# Bucket Filtering
		if filters.get("bucket"):
			data = [d for d in data if d.bucket == filters.get("bucket")]

	return data


def get_columns():
	return [
		{
			"label": "Source Application Number",
			"fieldtype": "Data",
			"fieldname": "loan_application"
		},
		{
			"label": "Loan",
			"fieldtype": "Link",
			"fieldname": "loan",
			"options": "Loan"
		},
		{
			"label": "Customer Relationship Id",
			"fieldtype": "",
			"fieldname": ""
		},{
			"label": "Applicant",
			"fieldtype": "Data",
			"fieldname": "applicant"
		},{
			"label": "Applicant Name",
			"fieldtype": "Data",
			"fieldname": "applicant_name"
		},
		{
			"label": "Applicant Type",
			"fieldtype": "Data",
			"fieldname": "applicant_type"
		},{
			"label": "Applicant Mobile",
			"fieldtype": "Data",
			"fieldname": "applicant_mobile"
		},
		{
			"label": "Sanction Date",
			"fieldtype": "Date",
			"fieldname": "posting_date"
		},
		{
			"label": "Sanction Amount",
			"fieldtype": "Currency",
			"fieldname": "loan_amount"
		},
		{
			"label": "Disbursement Date",
			"fieldtype": "Date",
			"fieldname": "disbursement_date"
		},
		{
			"label": "Disbursed Amount",
			"fieldtype": "Currency",
			"fieldname": "disbursed_amount"
		},
		{
			"label": "Email Id",
			"fieldtype": "Data",
			"fieldname": "email_id"
		},
		{
			"label": "Branch Code",
			"fieldtype": "Data",
			"fieldname": "branch_code"
		},
		{
			"label": "Branch Name",
			"fieldtype": "Data",
			"fieldname": "cost_center"
		},
		{
			"label": "Branch State",
			"fieldtype": "Data",
			"fieldname": "branch_state"
		},
		{
			"label": "Branch Zone",
			"fieldtype": "Data",
			"fieldname": "zone"
		},
		{
			"label": "Loan Status",
			"fieldtype": "Data",
			"fieldname": "loan_status"
		},{
			"label": "Applicant Address",
			"fieldtype": "Data",
			"fieldname": "applicant_address"
		},{
			"label": "EMI Date",
			"fieldtype": "Date",
			"fieldname": "due_date"
		},{
			"label": "Due Date",
			"fieldtype": "Data",
			"fieldname": "actual_due_date"
		},
		{
			"label": "EMI",
			"fieldtype": "Currency",
			"fieldname": "emi"
		},{
			"label": "Total Amount (Paid)",
			"fieldtype": "Currency",
			"fieldname": "total_amount"
		},{
			"label": "Bucket",
			"fieldtype": "Data",
			"fieldname": "bucket"
		},{
			"label": "Status",
			"fieldtype": "Data",
			"fieldname": "status"
		},{
			"label": "Action",
			"fieldtype": "Button",
			"fieldname": "action",
			"options": "dropdown",
			"width": "200px"
		}
	]
import json
@frappe.whitelist()
def GetFilteredData(DataArray, nach_type):
    finalObjList = []
    NachArray = json.loads(DataArray)
    #frappe.msgprint(repr(NachArray))
    for item in NachArray:
        loanNo = item["loan"]
        loanDoc = frappe.get_doc("Loan", loanNo)
        loanAppNo = loanDoc.loan_application
        loanAppDoc = frappe.get_doc("Loan Application", loanAppNo)
        sponsorBank = loanAppDoc.select_sponsor_bank
        
        if nach_type == 'AU Small Finance Bank':
            if sponsorBank == 'AU Small Finance Bank':
                UMRN = loanDoc.umrn

                ConsumerReferenceNo = loanNo
                Amount = item['emi']
                SettlementDate = item["due_date"]
                CreditAccountNo = "****************"
                objectToExport = {
                    'UMRN': UMRN,
                    'ConsumerReferenceNo': ConsumerReferenceNo,
                    'Amount': Amount,
                    'SettlementDate': SettlementDate,
                    'CreditAccountNo': CreditAccountNo
                }
                finalObjList.append(objectToExport)
        
        elif nach_type == 'HDFC':
            if sponsorBank == 'HDFC':
                UMRN = loanDoc.umrn

                UserNumber = "NACH00000000023302"
                SettlementDate = item["due_date"]
                Amount = item['emi']
                ConsumerReferenceNo = loanNo
                objectToExport = {
                    'UserNumber': UserNumber,
                    'SettlementDate': SettlementDate,
                    'UMRN': UMRN,
                    'Amount': Amount,
                    'ConsumerReferenceNo': ConsumerReferenceNo
                }
                finalObjList.append(objectToExport)   
        
        elif nach_type == 'FPDC':
            emiAmount = item['emi']
            bankName = loanDoc.bank_name_
            chequeNo = item['cheque_no']
            
            objectToExport = {
                'LoanNo': loanNo,
                'ChequeNo': chequeNo,
                'EMIamount': emiAmount,
                'BankName': bankName,
            }
            finalObjList.append(objectToExport)
    frappe.response.message = {'finalObjList': finalObjList}


@frappe.whitelist()
def GetHDFCFilteredData(DataArray):
    finalObjList = []
    EnachArray = json.loads(DataArray)
    for item in EnachArray:
        loanNo = item["loan"]
        loanDoc = frappe.get_doc("Loan", loanNo)
        loanAppNo = loanDoc.loan_application
        loanAppDoc = frappe.get_doc("Loan Application", loanAppNo)
        sponsorBank = loanAppDoc.select_sponsor_bank
        
        if sponsorBank == "HDFC":
            UserNumber = 'NACH00000000023302'
            SettlementDate = item["due_date"]
            # rec_enach_details = loanAppDoc.enach_details
            # UMRN = rec_enach_details[0]['umrn']
            UMRN = ''
            Amount = loanAppDoc.repayment_amount
            ReferenceNo = ''
            objectToExport = {
                'UserNumber': UserNumber,
                'SettlementDate': SettlementDate,
                'UMRN': UMRN,
                'Amount': Amount,
                'ReferenceNo': ReferenceNo
            }
            finalObjList.append(objectToExport)
    
    frappe.response.message = finalObjList

	


